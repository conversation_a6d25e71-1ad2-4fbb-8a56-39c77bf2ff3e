import React, { useState } from 'react';
import './App.css';
import IconGrid from './features/desktop/components/IconGrid';
import WindowFrame from './components/window/WindowFrame';

function App() {
  const [windows, setWindows] = useState([
    { id: 1, title: 'Window 1', content: 'Content 1' },
    { id: 2, title: 'Window 2', content: 'Content 2' },
  ]);

  const icons = [
    { id: 1, name: 'App 1' },
    { id: 2, name: 'App 2' },
  ];

  return (
    <>
      
        {windows.map((window) => (
          <WindowFrame key={window.id} id={window.id} title={window.title}>
            {window.content}
          </WindowFrame>
        ))}
        <IconGrid icons={icons} />
      
    </>
  );
}

export default App;
