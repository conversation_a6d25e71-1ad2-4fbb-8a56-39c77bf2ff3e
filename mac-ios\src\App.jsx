// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect, useRef } from 'react';
import 'remixicon/fonts/remixicon.css'
import {
    RiFolderLine,
    RiGlobalLine,
    RiMessage3Line,
    RiMailLine,
    RiCameraLine,
    RiHeadphoneLine,
    RiStore2Line,
    RiStickyNoteLine,
    RiCalendarLine,
    RiSettings3Line,
    RiTerminalLine,
    RiAppleLine,
    RiWifiLine,
    RiBatteryLine,
    RiSearchLine,
    RiListUnordered,
    RiApps2Line,
    RiArrowLeftSLine,
    RiArrowRightSLine,
    RiComputerLine,
    RiDownloadLine,
    RiFileTextLine,
    RiFileLine
} from 'react-icons/ri';

const App = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [draggedWindow, setDraggedWindow] = useState(null);
  const [activeWindow, setActiveWindow] = useState(null);
  const [windows, setWindows] = useState([
    {
      id: 1,
      title: 'Finder',
      content: 'This is the Finder window content.',
      minimized: false,
      position: { x: 100, y: 100 },
      size: { width: 600, height: 400 },
      zIndex: 1
    }
  ]);
  const [desktopIcons, setDesktopIcons] = useState([
    { id: 1, name: 'Finder', icon: RiFolderLine, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Safari', icon: RiGlobalLine, color: 'from-blue-500 to-blue-700' },
    { id: 3, name: 'Messages', icon: RiMessage3Line, color: 'from-green-400 to-green-600' },
    { id: 4, name: 'Mail', icon: RiMailLine, color: 'from-blue-400 to-indigo-600' },
    { id: 5, name: 'Photos', icon: RiCameraLine, color: 'from-purple-400 to-purple-600' },
    { id: 6, name: 'Music', icon: RiHeadphoneLine, color: 'from-pink-400 to-pink-600' },
    { id: 7, name: 'App Store', icon: RiStore2Line, color: 'from-blue-400 to-blue-600' },
    { id: 8, name: 'Notes', icon: RiStickyNoteLine, color: 'from-yellow-400 to-yellow-600' },
    { id: 9, name: 'Calendar', icon: RiCalendarLine, color: 'from-red-400 to-red-600' },
    { id: 10, name: 'Settings', icon: RiSettings3Line, color: 'from-gray-400 to-gray-600' },
  ]);
  const [dockApps, setDockApps] = useState([
    { id: 1, name: 'Finder', icon: RiFolderLine, isRunning: true, color: 'from-blue-400 to-blue-600' },
    { id: 2, name: 'Safari', icon: RiGlobalLine, isRunning: false, color: 'from-blue-500 to-blue-700' },
    { id: 3, name: 'Messages', icon: RiMessage3Line, isRunning: false, color: 'from-green-400 to-green-600' },
    { id: 4, name: 'Mail', icon: RiMailLine, isRunning: false, color: 'from-blue-400 to-indigo-600' },
    { id: 5, name: 'Photos', icon: RiCameraLine, isRunning: false, color: 'from-purple-400 to-purple-600' },
    { id: 6, name: 'Terminal', icon: RiTerminalLine, isRunning: false, color: 'from-gray-700 to-gray-900' },
    { id: 7, name: 'App Store', icon: RiStore2Line, isRunning: false, color: 'from-blue-400 to-blue-600' },
    { id: 8, name: 'Settings', icon: RiSettings3Line, isRunning: false, color: 'from-gray-400 to-gray-600' },
  ]);
  const dragOffset = useRef({ x: 0, y: 0 });
  const maxZIndex = useRef(1);

  const handleMouseDown = (e, windowId) => {
    const windowObj = windows.find(w => w.id === windowId);
    if (!windowObj) return;
    bringToFront(windowId);
    setIsDragging(true);
    setDraggedWindow(windowId);
    const rect = e.currentTarget.getBoundingClientRect();
    dragOffset.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  const handleMouseMove = (e) => {
    if (!isDragging || draggedWindow === null) return;
    setWindows(prevWindows =>
      prevWindows.map(window => {
        if (window.id === draggedWindow) {
          return {
            ...window,
            position: {
              x: e.clientX - dragOffset.current.x,
              y: e.clientY - dragOffset.current.y
            }
          };
        }
        return window;
      })
    );
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDraggedWindow(null);
  };

  const bringToFront = (windowId) => {
    maxZIndex.current += 1;
    setActiveWindow(windowId);
    setWindows(prevWindows =>
      prevWindows.map(window => {
        if (window.id === windowId) {
          return {
            ...window,
            zIndex: maxZIndex.current
          };
        }
        return window;
      })
    );
  };

  const minimizeWindow = (windowId) => {
    setWindows(prevWindows =>
      prevWindows.map(window => {
        if (window.id === windowId) {
          return {
            ...window,
            minimized: true
          };
        }
        return window;
      })
    );
  };

  const closeWindow = (windowId) => {
    setWindows(prevWindows => prevWindows.filter(window => window.id !== windowId));
    setDockApps(prevDockApps =>
      prevDockApps.map(app => {
        if (app.id === windowId) {
          return { ...app, isRunning: false };
        }
        return app;
      })
    );
  };

  const openApp = (appId) => {
    const existingWindow = windows.find(w => w.id === appId);
    if (existingWindow) {
      if (existingWindow.minimized) {
        setWindows(prevWindows =>
          prevWindows.map(window => {
            if (window.id === appId) {
              return {
                ...window,
                minimized: false
              };
            }
            return window;
          })
        );
      }
      bringToFront(appId);
      return;
    }
    const app = dockApps.find(a => a.id === appId) || desktopIcons.find(a => a.id === appId);
    if (!app) return;
    const newWindow = {
      id: appId,
      title: app.name,
      content: `This is the ${app.name} window content.`,
      minimized: false,
      position: { x: 150 + (windows.length * 30), y: 150 + (windows.length * 20) },
      size: { width: 600, height: 400 },
      zIndex: maxZIndex.current + 1
    };
    maxZIndex.current += 1;
    setWindows(prevWindows => [...prevWindows, newWindow]);
    setActiveWindow(appId);
    setDockApps(prevDockApps =>
      prevDockApps.map(dockApp => {
        if (dockApp.id === appId) {
          return { ...dockApp, isRunning: true };
        }
        return dockApp;
      })
    );
  };
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
    // eslint-disable-next-line
  }, [isDragging]);

  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
  };

  const [time, setTime] = useState(getCurrentTime());

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(getCurrentTime());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  });
  return (
    <div className="relative min-h-screen bg-cover bg-center overflow-hidden"
      style={{
        backgroundImage: `url('https://readdy.ai/api/search-image?query=Stunning%20macOS%20desktop%20wallpaper%20with%20soft%20gradient%20colors%2C%20mountains%20in%20the%20distance%2C%20beautiful%20sunset%20sky%20with%20pink%20and%20purple%20hues%2C%20ultra%20high%20definition%2C%20professional%20photography%2C%20serene%20landscape&width=1920&height=1080&seq=1&orientation=landscape')`,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
      }}>
      {/* Top Menu Bar */}
      <div className="fixed top-0 left-0 right-0 h-8 bg-black bg-opacity-20 backdrop-blur-md z-50 flex items-center justify-between px-4 text-white">
        <div className="flex items-center space-x-4">
          <div className="cursor-pointer">
            <RiAppleLine className="text-lg" />
          </div>
          <div className="font-medium">{activeWindow ? (windows.find(w => w.id === activeWindow) || {}).title : 'Finder'}</div>
          <div className="hidden sm:block">File</div>
          <div className="hidden sm:block">Edit</div>
          <div className="hidden sm:block">View</div>
          <div className="hidden sm:block">Go</div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="hidden sm:block"><RiWifiLine className="text-sm" /></div>
          <div className="hidden sm:block"><RiBatteryLine className="text-sm" /></div>
          <div>{time}</div>
        </div>
      </div>
      {/* Desktop Icons */}
      <div className="pt-12 px-6 grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 gap-6">
        {desktopIcons.map(icon => {
          const IconComponent = icon.icon;
          return (
            <div
              key={icon.id}
              className="flex flex-col items-center cursor-pointer group"
              onClick={() => openApp(icon.id)}
            >
              <div className={`w-16 h-16 flex items-center justify-center text-white bg-gradient-to-b ${icon.color} rounded-2xl mb-2 shadow-lg transform group-hover:scale-105 transition-all duration-200 backdrop-blur-sm bg-opacity-90`}>
                <IconComponent className="text-2xl drop-shadow-md" />
              </div>
              <span className="text-white text-sm text-center px-1 py-0.5 rounded bg-black bg-opacity-30 max-w-full truncate whitespace-nowrap">
                {icon.name}
              </span>
            </div>
          );
        })}
      </div>
      {/* Windows */}
      {windows.filter(window => !window.minimized).map(window => (
        <div
          key={window.id}
          className={`absolute rounded-lg shadow-2xl overflow-hidden ${activeWindow === window.id ? 'ring-2 ring-blue-500' : 'opacity-95'}`}
          style={{
            left: window.position.x,
            top: window.position.y,
            width: window.size.width,
            height: window.size.height,
            zIndex: window.zIndex
          }}
          onClick={() => bringToFront(window.id)}
        >
          {/* Window Title Bar */}
          <div
            className="h-8 bg-gray-200 dark:bg-gray-800 flex items-center px-3 cursor-move"
            onMouseDown={(e) => handleMouseDown(e, window.id)}
          >
            <div className="flex space-x-2 mr-4">
              <button
                className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-600 !rounded-button whitespace-nowrap cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  closeWindow(window.id);
                }}
              ></button>
              <button
                className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-600 !rounded-button whitespace-nowrap cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  const currentWindow = windows.find(w => w.id === window.id);
                  if (currentWindow?.size.width === window.innerWidth) {
                    setWindows(prevWindows =>
                      prevWindows.map(w => {
                        if (w.id === window.id) {
                          return {
                            ...w,
                            position: w.prevPosition || { x: 150, y: 150 },
                            size: w.prevSize || { width: 600, height: 400 }
                          };
                        }
                        return w;
                      })
                    );
                  } else {
                    setWindows(prevWindows =>
                      prevWindows.map(w => {
                        if (w.id === window.id) {
                          return {
                            ...w,
                            prevPosition: w.position,
                            prevSize: w.size,
                            position: { x: 0, y: 32 },
                            size: { width: window.innerWidth, height: window.innerHeight - 32 - 80 }
                          };
                        }
                        return w;
                      })
                    );
                  }
                }}
              ></button>
              <button
                className="w-3 h-3 rounded-full bg-green-500 hover:bg-green-600 !rounded-button whitespace-nowrap cursor-pointer"
              ></button>
            </div>
            <div className="text-center flex-1 text-sm font-medium text-gray-700 dark:text-gray-300">
              {window.title}
            </div>
          </div>
          {/* Window Content */}
          <div className="bg-white dark:bg-gray-900 h-[calc(100%-2rem)] p-4 overflow-auto">
            <div className="flex items-center mb-4 space-x-2">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    className="w-full bg-gray-100 dark:bg-gray-800 rounded-lg py-2 px-4 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 border-none"
                    placeholder="Search in window..."
                  />
                  <RiSearchLine className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
              </div>
              <button className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg !rounded-button whitespace-nowrap">
                <RiListUnordered />
              </button>
              <button className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg !rounded-button whitespace-nowrap">
                <RiApps2Line />
              </button>
            </div>
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
              Welcome to {window.title}
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              {window.content}
            </p>
            {window.title === 'Finder' && (
              <div className="mt-4 border rounded-lg overflow-hidden">
                <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 flex justify-between items-center border-b">
                  <div className="flex space-x-4">
                    <button className="text-gray-600 dark:text-gray-400 !rounded-button whitespace-nowrap cursor-pointer">
                      <RiArrowLeftSLine />
                    </button>
                    <button className="text-gray-600 dark:text-gray-400 !rounded-button whitespace-nowrap cursor-pointer">
                      <RiArrowRightSLine />
                    </button>
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="relative">
                      <input
                        type="text"
                        className="w-full bg-gray-200 dark:bg-gray-700 rounded-md py-1 px-3 pr-8 text-sm border-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Search"
                      />
                      <RiSearchLine className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm" />
                    </div>
                  </div>
                  <div>
                    <button className="text-gray-600 dark:text-gray-400 !rounded-button whitespace-nowrap cursor-pointer">
                      <RiApps2Line />
                    </button>
                  </div>
                </div>
                <div className="flex h-64">
                  <div className="w-48 bg-gray-50 dark:bg-gray-800 border-r">
                    <div className="p-2 text-sm">
                      <div className="font-medium mb-2 text-gray-700 dark:text-gray-300">Favorites</div>
                      <div className="pl-2 space-y-1">
                        <div className="flex items-center space-x-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer">
                          <RiComputerLine className="text-blue-500" />
                          <span className="text-gray-600 dark:text-gray-400">Desktop</span>
                        </div>
                        <div className="flex items-center space-x-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer">
                          <RiDownloadLine className="text-blue-500" />
                          <span className="text-gray-600 dark:text-gray-400">Downloads</span>
                        </div>
                        <div className="flex items-center space-x-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer">
                          <RiFileLine className="text-blue-500" />
                          <span className="text-gray-600 dark:text-gray-400">Documents</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 p-3 grid grid-cols-3 gap-3">
                    {[1, 2, 3, 4, 5, 6].map(item => (
                      <div key={item} className="flex flex-col items-center p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                        <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center mb-1">
                          <RiFileTextLine className="text-blue-500" />
                        </div>
                        <span className="text-xs text-gray-600 dark:text-gray-400 text-center">File {item}.txt</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
      {/* Dock */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 px-2 py-1 rounded-2xl bg-white bg-opacity-20 backdrop-blur-md flex items-end space-x-2 shadow-lg">
        {dockApps.map(app => {
          const IconComponent = app.icon;
          return (
            <div
              key={app.id}
              className="relative group cursor-pointer transition-all duration-200 ease-in-out transform hover:scale-110 hover:-translate-y-2"
              onClick={() => openApp(app.id)}
            >
              <div className={`w-12 h-12 bg-gradient-to-b ${app.color} rounded-2xl flex items-center justify-center shadow-lg backdrop-blur-sm bg-opacity-90`}>
                <IconComponent className="text-white text-xl drop-shadow-md" />
              </div>
              {app.isRunning && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"></div>
              )}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 whitespace-nowrap bg-black bg-opacity-75 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                {app.name}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default App;