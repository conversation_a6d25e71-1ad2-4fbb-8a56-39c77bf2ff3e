.window {
  position: absolute;
  top: 50px;
  left: 50px;
  width: 400px;
  height: 300px;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  background-color: #eee;
  border-bottom: 1px solid #ccc;
}

.window-title {
  font-size: 14px;
  font-weight: bold;
}

.window-controls {
  display: flex;
}

.window-controls button {
  margin-left: 5px;
  padding: 3px 5px;
  font-size: 12px;
}

.window-content {
  padding: 10px;
}
