import React from 'react';
import './WindowFrame.css';

/**
 * @param {Object} props
 * @param {string} props.id - The id of the window
 * @param {string} props.title - The title of the window
 * @param {React.ReactNode} props.children - The content of the window
 */
const WindowFrame = ({ id, title, children }) => {
  return (
    <div className="window">
      <div className="window-header">
        <div className="window-title">{title}</div>
        <div className="window-controls">
          <button>Close</button>
          <button>Minimize</button>
          <button>Maximize</button>
        </div>
      </div>
      <div className="window-content">
        {children}
      </div>
    </div>
  );
};

export default WindowFrame;
